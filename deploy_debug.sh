#!/bin/bash

# 简化的部署脚本用于调试
# 使用方法: ./deploy_debug.sh <ssh_host>

set -e

SSH_HOST=$1
PORT=5000
PROJECT_NAME="caokong_vz"

if [ $# -lt 1 ]; then
    echo "使用方法: $0 <ssh_host>"
    exit 1
fi

echo "🚀 开始调试部署到: $SSH_HOST"

# 获取远程目录
REMOTE_DIR="/home/<USER>/deploy/$PROJECT_NAME"

echo "📁 部署目录: $REMOTE_DIR"

# 停止现有服务
echo "🛑 停止现有服务..."
ssh "$SSH_HOST" "pkill -f 'python.*app.py' 2>/dev/null || true"

# 创建目录并上传文件
echo "📤 上传文件..."
ssh "$SSH_HOST" "mkdir -p '$REMOTE_DIR'/{templates,static,logs}"
scp -r app.py requirements.txt templates/ static/ "$SSH_HOST:$REMOTE_DIR/"

# 安装依赖和启动服务（分步执行以便调试）
echo "🔧 安装依赖..."
ssh "$SSH_HOST" "cd '$REMOTE_DIR' && python3 -m venv venv && source venv/bin/activate && pip install -r requirements.txt"

echo "🚀 启动服务..."
ssh "$SSH_HOST" "cd '$REMOTE_DIR' && source venv/bin/activate && nohup python app.py > logs/service.log 2>&1 & echo \$! > app.pid"

echo "⏳ 等待服务启动..."
sleep 5

# 检查服务状态
echo "🔍 检查服务状态..."
ssh "$SSH_HOST" "cd '$REMOTE_DIR' && if [ -f app.pid ] && kill -0 \$(cat app.pid) 2>/dev/null; then echo '✅ 服务运行中 PID:' \$(cat app.pid); else echo '❌ 服务未运行'; cat logs/service.log; fi"

echo "🌐 获取访问地址..."
SERVER_IP=$(ssh "$SSH_HOST" "curl -s --connect-timeout 5 http://***************/latest/meta-data/public-ipv4 2>/dev/null || hostname -I | awk '{print \$1}'")
echo "访问地址: http://$SERVER_IP:$PORT"

echo "✅ 调试部署完成"
