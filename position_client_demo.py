#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仓位上报客户端Demo
提供完整的错误处理，确保不影响主程序运行
"""

import requests
import json
import time
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any

# 北京时区（东八区）
BEIJING_TZ = timezone(timedelta(hours=8))

def get_beijing_time_str():
    """获取北京时间字符串"""
    return datetime.now(BEIJING_TZ).strftime("%Y-%m-%d %H:%M:%S")


class PositionReportClient:
    """仓位上报客户端 - 带完整错误处理"""

    def __init__(self, exchange_name, base_url: str = "http://**************:5000", timeout: int = 5):
        """
        初始化客户端

        Args:
            base_url: 服务器地址
            timeout: 请求超时时间(秒)
        """
        self.base_url = base_url.rstrip('/')
        self.api_url = f"{self.base_url}/api/position"
        self.positions_url = f"{self.base_url}/api/positions"
        self.timeout = timeout
        self.session = requests.Session()
        self.exchange_name = exchange_name

    def _safe_request(self, data: Dict[str, Any]) -> bool:
        """
        安全的请求发送，包含完整错误处理

        Args:
            data: 要发送的数据

        Returns:
            bool: 是否发送成功
        """
        try:
            # 数据验证
            if not isinstance(data, dict):
                self.logger.error("数据格式错误: 必须是字典类型")
                return False

            # 发送请求
            response = self.session.post(
                self.api_url,
                json=data,
                timeout=self.timeout,
                headers={'Content-Type': 'application/json'}
            )

            # 检查响应状态
            if response.status_code == 200:
                try:
                    result = response.json()
                    return True
                except json.JSONDecodeError:
                    return False
            else:
                return False

        except requests.exceptions.Timeout:
            return False
        except requests.exceptions.ConnectionError:
            return False
        except requests.exceptions.RequestException as e:
            return False
        except Exception as e:
            return False

    def open_position(self, symbol: str, position_side: str,
                     open_price: float, target_quantity: float,
                     current_price: Optional[float] = None) -> bool:
        """
        开始开仓

        Args:
            symbol: 交易对 (如: BTCUSDT, ETHUSDT)
            position_side: 仓位方向 (LONG 或 SHORT)
            open_price: 开仓价格
            target_quantity: 目标数量
            current_price: 当前价格 (可选，默认使用开仓价格)

        Returns:
            bool: 是否开仓成功
        """
        try:
            if current_price is None:
                current_price = open_price

            data = {
                "exchange": str(self.exchange_name),
                "symbol": str(symbol),
                "action": "open_pos",
                "position": {
                    "time": get_beijing_time_str(),
                    "open_price": str(open_price),
                    "open_quantity": str(target_quantity),
                    "open_avg_price": str(open_price),
                    "current_quantity": "0",
                    "position_side": str(position_side).upper(),
                    "current_price": str(current_price)
                }
            }

            return self._safe_request(data)

        except Exception as e:
            return False

    def get_position_data(self, symbol: str, position_side: str) -> Optional[Dict[str, Any]]:
        """
        获取当前持仓数据

        Args:
            exchange: 交易所名称
            symbol: 交易对
            position_side: 仓位方向

        Returns:
            Dict: 持仓数据，如果不存在返回None
        """
        try:
            response = self.session.get(self.positions_url, timeout=self.timeout)
            if response.status_code == 200:
                positions = response.json()
                return positions.get(self.exchange_name, {}).get(symbol, {}).get(position_side.upper())
            return None
        except Exception as e:
            return None

    def update_position(self, symbol: str, position_side: str,
                       current_quantity: float, avg_price: Optional[float] = None,
                       current_price: Optional[float] = None) -> bool:
        """
        更新仓位

        Args:
            exchange: 交易所名称
            symbol: 交易对
            position_side: 仓位方向 (LONG 或 SHORT)
            current_quantity: 当前数量
            avg_price: 平均价格 (可选，从已存储数据获取)
            current_price: 当前价格 (可选，从已存储数据获取)

        Returns:
            bool: 是否更新成功
        """
        try:
            existing_data = self.get_position_data(symbol, position_side)
            if not existing_data:
                print(f"错误: 未找到 {self.exchange_name} {symbol} {position_side} 的持仓数据，请先调用 open_position")
                return False

            # 从已存储数据中获取必要信息
            open_price = float(existing_data.get("open_price", 0))
            target_quantity = float(existing_data.get("open_quantity", 0))

            if avg_price is None:
                avg_price = float(existing_data.get("open_avg_price", open_price))
            if current_price is None:
                current_price = float(existing_data.get("current_price", open_price))

            data = {
                "exchange": str(self.exchange_name),
                "symbol": str(symbol),
                "action": "update_pos",
                "position": {
                    "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "open_price": str(open_price),
                    "open_quantity": str(target_quantity),
                    "open_avg_price": str(avg_price),
                    "current_quantity": str(current_quantity),
                    "position_side": str(position_side).upper(),
                    "current_price": str(current_price)
                }
            }

            return self._safe_request(data)

        except Exception as e:
            print(f"更新仓位失败: {e}")
            return False

    def report_long_position(self, exchange: str, symbol: str,
                           open_price: float, quantity: float,
                           avg_price: Optional[float] = None,
                           current_price: Optional[float] = None) -> bool:
        """
        上报多头(LONG)仓位

        Args:
            exchange: 交易所名称
            symbol: 交易对
            open_price: 开仓价格
            quantity: 仓位数量
            avg_price: 平均价格
            current_price: 当前价格

        Returns:
            bool: 是否上报成功
        """
        try:
            return self.update_position(
                exchange=exchange,
                symbol=symbol,
                position_side="LONG",
                current_quantity=quantity,
                avg_price=avg_price,
                current_price=current_price
            )
        except Exception as e:
            return False

    def report_short_position(self, exchange: str, symbol: str,
                            open_price: float, quantity: float,
                            avg_price: Optional[float] = None,
                            current_price: Optional[float] = None) -> bool:
        """
        上报空头(SHORT)仓位

        Args:
            exchange: 交易所名称
            symbol: 交易对
            open_price: 开仓价格
            quantity: 仓位数量
            avg_price: 平均价格
            current_price: 当前价格

        Returns:
            bool: 是否上报成功
        """
        try:
            return self.update_position(
                exchange=exchange,
                symbol=symbol,
                position_side="SHORT",
                current_quantity=quantity,
                avg_price=avg_price,
                current_price=current_price
            )
        except Exception as e:
            return False

    def update_position_by_trade(self, symbol: str, order_id: str, position_side: str,
                                price: float, acc_quantity: float,
                                exchange: Optional[str] = None) -> bool:
        """
        通过trade更新仓位

        Args:
            symbol: 交易对
            order_id: 订单ID
            position_side: 仓位方向 (LONG 或 SHORT)
            price: 成交价格
            acc_quantity: 累计成交数量
            exchange: 交易所名称 (可选，默认使用初始化时的exchange_name)

        Returns:
            bool: 是否更新成功
        """
        try:
            if exchange is None:
                exchange = self.exchange_name

            # 构建请求数据
            data = {
                "exchange": str(exchange),
                "symbol": str(symbol),
                "order_id": str(order_id),
                "position_side": str(position_side).upper(),
                "price": float(price),
                "acc_quantity": float(acc_quantity)
            }

            # 发送到新的API端点
            try:
                response = self.session.post(
                    f"{self.base_url}/api/update_position_by_trade",
                    json=data,
                    timeout=self.timeout,
                    headers={'Content-Type': 'application/json'}
                )

                if response.status_code == 200:
                    result = response.json()
                    operation_type = result.get('operation_type', '更新')
                    actual_change = result.get('actual_quantity_change', 'N/A')
                    new_quantity = result.get('new_current_quantity', 'N/A')

                    # 根据变化量显示不同的图标
                    if isinstance(actual_change, (int, float)):
                        if actual_change > 0:
                            icon = "📈"  # 增仓
                        elif actual_change < 0:
                            icon = "📉"  # 减仓
                        else:
                            icon = "➡️"   # 无变化
                    else:
                        icon = "✅"

                    print(f"{icon} Trade{operation_type}成功: {symbol} {position_side}, order_id: {order_id}, "
                          f"累计数量: {acc_quantity}, 实际变化: {actual_change}, 当前仓位: {new_quantity}")
                    return True
                else:
                    error_msg = response.text
                    try:
                        error_data = response.json()
                        error_msg = error_data.get('error', error_msg)
                    except:
                        pass
                    print(f"❌ Trade更新失败: {error_msg}")
                    return False

            except requests.exceptions.Timeout:
                print(f"❌ Trade更新超时: {symbol} {position_side}")
                return False
            except requests.exceptions.ConnectionError:
                print(f"❌ Trade更新连接失败: {symbol} {position_side}")
                return False
            except Exception as e:
                print(f"❌ Trade更新请求异常: {e}")
                return False

        except Exception as e:
            print(f"❌ Trade更新失败: {e}")
            return False

    def get_all_positions(self) -> Optional[Dict]:
        """
        获取所有仓位数据

        Returns:
            Dict: 所有仓位数据，失败时返回None
        """
        try:
            response = self.session.get(f"{self.base_url}/api/positions", timeout=self.timeout)
            if response.status_code == 200:
                return response.json()
            else:
                return None
        except Exception as e:
            return None

    def clear_all_positions(self) -> bool:
        """
        清除所有仓位数据

        Returns:
            bool: 是否清除成功
        """
        try:
            response = self.session.post(
                f"{self.base_url}/api/positions/clear",
                timeout=self.timeout,
                headers={'Content-Type': 'application/json'}
            )

            if response.status_code == 200:
                try:
                    result = response.json()
                    self.logger.info(f"清除成功: {result.get('message', '所有仓位数据已清除')}")
                    return True
                except json.JSONDecodeError:
                    self.logger.error("服务器响应格式错误")
                    return False
            else:
                self.logger.error(f"清除失败: {response.status_code}")
                return False

        except requests.exceptions.Timeout:
            self.logger.error(f"清除请求超时 (>{self.timeout}秒)")
            return False
        except requests.exceptions.ConnectionError:
            self.logger.error("清除失败: 无法连接到服务器")
            return False
        except requests.exceptions.RequestException as e:
            self.logger.error(f"清除请求异常: {str(e)}")
            return False
        except Exception as e:
            self.logger.error(f"清除数据时发生未知错误: {str(e)}")
            return False


def simulate_gradual_opening(client, exchange: str, symbol: str, position_side: str,
                           open_price: float, target_quantity: float,
                           current_price: float = None):
    """
    模拟逐步开仓过程

    Args:
        client: PositionReportClient实例
        exchange: 交易所名称
        symbol: 交易对
        position_side: 仓位方向
        open_price: 开仓价格
        target_quantity: 目标数量
        current_price: 当前价格
    """
    if current_price is None:
        current_price = open_price

    print(f"\n🚀 开始模拟 {position_side} 仓位开仓过程")
    print(f"交易对: {exchange} {symbol}")
    print(f"目标数量: {target_quantity}")
    print("=" * 60)

    # 1. 开始开仓
    success = client.open_position(
        symbol=symbol,
        position_side=position_side,
        open_price=open_price,
        target_quantity=target_quantity,
        current_price=current_price
    )
    print(f"📍 开仓开始: {'成功' if success else '失败'}")

    if not success:
        print("❌ 开仓失败，停止模拟")
        return False

    # 2. 模拟逐步开仓过程 (每秒更新一次，直到95%以上)
    steps = 20  # 分20步完成开仓
    for i in range(1, steps + 1):
        # 计算当前仓位进度
        progress = i / steps
        current_quantity = target_quantity * progress

        # 模拟价格波动
        price_variation = current_price * 0.001 * (i % 3 - 1)  # ±0.1%的价格波动
        current_market_price = current_price + price_variation

        # 更新仓位
        success = client.update_position(
            symbol=symbol,
            position_side=position_side,
            current_quantity=current_quantity,
            avg_price=open_price,
            current_price=current_market_price
        )

        progress_percent = progress * 100
        print(f"📊 步骤 {i:2d}/20: 仓位进度 {progress_percent:5.1f}% "
              f"({current_quantity:.3f}/{target_quantity}) "
              f"价格: {current_market_price:.2f} "
              f"{'✅' if success else '❌'}")

        # 如果达到95%以上，再更新几次然后停止
        if progress >= 0.95:
            print(f"🎯 已达到95%以上，再更新2次后停止")
            for j in range(2):
                time.sleep(1)
                # 继续小幅更新
                final_progress = min(1.0, progress + (j + 1) * 0.01)
                final_quantity = target_quantity * final_progress

                success = client.update_position(
                    exchange=exchange,
                    symbol=symbol,
                    position_side=position_side,
                    current_quantity=final_quantity,
                    avg_price=open_price,
                    current_price=current_market_price
                )

                final_percent = final_progress * 100
                print(f"📊 最终步骤 {j+1}: 仓位进度 {final_percent:5.1f}% "
                      f"({final_quantity:.3f}/{target_quantity}) "
                      f"{'✅' if success else '❌'}")
            break

        # 每秒更新一次
        time.sleep(1)

    print(f"✅ {position_side} 仓位开仓模拟完成！")
    return True


def simulate_gradual_closing(client, exchange: str, symbol: str, position_side: str,
                           open_price: float, target_quantity: float,
                           current_price: float = None):
    """
    模拟逐步清仓过程

    Args:
        client: PositionReportClient实例
        exchange: 交易所名称
        symbol: 交易对
        position_side: 仓位方向
        open_price: 开仓价格
        target_quantity: 目标数量
        current_price: 当前价格
    """
    if current_price is None:
        current_price = open_price

    print(f"\n🔥 开始模拟 {position_side} 仓位清仓过程")
    print(f"交易对: {exchange} {symbol}")
    print(f"目标数量: {target_quantity}")
    print("=" * 60)

    # 先持仓一段时间 (保持在95%以上)
    print("💎 持仓阶段 (保持95%以上)...")
    for i in range(5):
        # 保持在95%以上
        current_quantity = target_quantity * 0.98

        # 模拟价格波动
        price_variation = current_price * 0.002 * (i % 3 - 1)  # ±0.2%的价格波动
        current_market_price = current_price + price_variation

        success = client.update_position(
            exchange=exchange,
            symbol=symbol,
            position_side=position_side,
            current_quantity=current_quantity,
            avg_price=open_price,
            current_price=current_market_price
        )

        print(f"💎 持仓 {i+1}/5: 仓位 98.0% ({current_quantity:.3f}/{target_quantity}) "
              f"价格: {current_market_price:.2f} {'✅' if success else '❌'}")
        time.sleep(1)

    # 开始清仓过程 (从95%以下开始计算清仓耗时)
    print("\n🔥 开始清仓 (从95%以下到5%)...")
    steps = 15  # 分15步完成清仓

    for i in range(1, steps + 1):
        # 计算清仓进度 (从95%降到5%)
        remaining_progress = 0.95 - (0.9 * i / steps)  # 从95%降到5%
        remaining_progress = max(0.05, remaining_progress)  # 最低5%

        current_quantity = target_quantity * remaining_progress

        # 模拟价格波动
        price_variation = current_price * 0.002 * (i % 3 - 1)
        current_market_price = current_price + price_variation

        # 更新仓位
        success = client.update_position(
            exchange=exchange,
            symbol=symbol,
            position_side=position_side,
            current_quantity=current_quantity,
            avg_price=open_price,
            current_price=current_market_price
        )

        progress_percent = remaining_progress * 100
        print(f"🔥 清仓步骤 {i:2d}/15: 仓位进度 {progress_percent:5.1f}% "
              f"({current_quantity:.3f}/{target_quantity}) "
              f"价格: {current_market_price:.2f} "
              f"{'✅' if success else '❌'}")

        # 如果降到5%，再更新几次然后停止
        if remaining_progress <= 0.05:
            print(f"🎯 已降到5%，再更新2次后停止")
            for j in range(2):
                time.sleep(1)
                # 继续小幅减少
                final_progress = max(0.01, remaining_progress - (j + 1) * 0.01)
                final_quantity = target_quantity * final_progress

                success = client.update_position(
                    exchange=exchange,
                    symbol=symbol,
                    position_side=position_side,
                    current_quantity=final_quantity,
                    avg_price=open_price,
                    current_price=current_market_price
                )

                final_percent = final_progress * 100
                print(f"🔥 最终步骤 {j+1}: 仓位进度 {final_percent:5.1f}% "
                      f"({final_quantity:.3f}/{target_quantity}) "
                      f"{'✅' if success else '❌'}")
            break

        # 每秒更新一次
        time.sleep(1)

    print(f"✅ {position_side} 仓位清仓模拟完成！")
    return True


# 使用示例
if __name__ == "__main__":
    # 创建客户端
    client = PositionReportClient("test_exchang", "http://**************:5000")

    print("🚀 仓位上报客户端Demo - 完整开仓清仓模拟")
    print("=" * 70)

    # 清除所有现有仓位
    print("🧹 清除所有现有仓位...")
    try:
        clear_success = client.clear_all_positions()
        print(f"清除结果: {'成功' if clear_success else '失败'}")
    except:
        print("清除失败，继续执行...")

    time.sleep(2)

    # 1. 模拟LONG仓位完整流程
    print("\n" + "=" * 70)
    print("📈 LONG仓位完整流程演示")
    print("=" * 70)

    # 开仓
    opening_success = simulate_gradual_opening(
        client=client,
        exchange="binance",
        symbol="BTCUSDT",
        position_side="LONG",
        open_price=50000,
        target_quantity=2.0,
        current_price=50000
    )

    if opening_success:
        print("\n等待10秒后开始清仓...")
        time.sleep(10)

        # 清仓
        simulate_gradual_closing(
            client=client,
            exchange="binance",
            symbol="BTCUSDT",
            position_side="LONG",
            open_price=50000,
            target_quantity=2.0,
            current_price=50500  # 模拟价格上涨
        )

    # 2. 模拟SHORT仓位完整流程
    print("\n" + "=" * 70)
    print("📉 SHORT仓位完整流程演示")
    print("=" * 70)
    print("等待5秒后开始SHORT仓位模拟...")
    time.sleep(5)

    # 开仓
    opening_success = simulate_gradual_opening(
        client=client,
        exchange="okx",
        symbol="ETHUSDT",
        position_side="SHORT",
        open_price=3200,
        target_quantity=5.0,
        current_price=3200
    )

    if opening_success:
        print("\n等待10秒后开始清仓...")
        time.sleep(10)

        # 清仓
        simulate_gradual_closing(
            client=client,
            exchange="okx",
            symbol="ETHUSDT",
            position_side="SHORT",
            open_price=3200,
            target_quantity=5.0,
            current_price=3150  # 模拟价格下跌
        )

    print("\n" + "=" * 70)
    print("✅ 所有模拟完成！")
    print("📊 访问 http://**************:5000 查看完整结果")
    print("📈 开仓耗时: 从open_pos到95%的时间")
    print("💎 持仓时间: 仓位在95%以上的时间")
    print("📉 清仓耗时: 从95%以下到5%的时间")
    print("=" * 70)
