#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
import threading
import time
from datetime import datetime, timezone, timedelta
from flask import Flask, request, jsonify, render_template
from flask_socketio import SocketIO, emit
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 北京时区（东八区）
BEIJING_TZ = timezone(timedelta(hours=8))

def get_beijing_time():
    """获取北京时间"""
    return datetime.now(BEIJING_TZ)

def get_beijing_time_str():
    """获取北京时间字符串"""
    return get_beijing_time().strftime("%Y-%m-%d %H:%M:%S")

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
socketio = SocketIO(app, cors_allowed_origins="*")

# 内存存储 - 存储当前持仓数据
# 格式: {exchange: {symbol: {LONG: position_data, SHORT: position_data}}}
positions_data = {}

# 内存存储 - 存储trade数据，用于跟踪order_id和累计数量
# 格式: {exchange: {symbol: {position_side: {order_id: {"last_acc_quantity": float, "last_update_time": str, "total_trades": int}}}}}
trade_data = {}

data_lock = threading.Lock()

# 日志文件路径
LOG_DIR = "logs"
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)

def log_to_file(data):
    """将数据记录到日志文件"""
    beijing_time = get_beijing_time()
    timestamp = beijing_time.strftime("%Y%m%d")
    log_file = os.path.join(LOG_DIR, f"positions_{timestamp}.log")

    log_entry = {
        "timestamp": beijing_time.isoformat(),
        "data": data
    }

    try:
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(json.dumps(log_entry, ensure_ascii=False) + "\n")
    except Exception as e:
        logger.error(f"写入日志文件失败: {e}")

def calculate_position_metrics(position_data, existing_data=None):
    """计算持仓相关指标"""
    try:
        open_quantity = float(position_data.get("open_quantity", 0))
        current_quantity = float(position_data.get("current_quantity", 0))
        open_avg_price = float(position_data.get("open_avg_price", 0))
        current_price = float(position_data.get("current_price", open_avg_price))  # 如果没有当前价格，使用开仓价格

        # 计算进度比例
        progress = (current_quantity / open_quantity * 100) if open_quantity > 0 else 0

        # 计算浮动盈亏
        position_side = position_data.get("position_side", "LONG").upper()
        if position_side == "LONG":
            pnl = (current_price - open_avg_price) * current_quantity
        else:  # SHORT
            pnl = (open_avg_price - current_price) * current_quantity

        # 时间相关计算
        current_time_str = position_data.get("time", "")
        if current_time_str:
            # 解析时间字符串，假设输入的时间已经是北京时间
            current_time = datetime.strptime(current_time_str, "%Y-%m-%d %H:%M:%S")
            # 为了计算时间差，需要添加时区信息
            current_time = current_time.replace(tzinfo=BEIJING_TZ)
        else:
            current_time = get_beijing_time()

        # 初始化时间字段
        result = {
            "progress": min(progress, 100),  # 限制最大100%
            "pnl": pnl,
            "is_profit": pnl > 0,
            "open_time": current_time_str,
            "open_start_time": None,
            "position_95_time": None,        # 达到95%的时间
            "position_below_95_time": None,  # 从95%以上降到95%以下的时间
            "position_5_time": None,         # 降到5%的时间
            "position_peak_time": None,      # 达到峰值仓位的时间
            "position_start_closing_time": None,  # 开始清仓的时间（仓位开始减少）
            "peak_progress": 0,              # 峰值仓位进度
            "opening_duration": 0,           # 开仓耗时（从open_pos到95%）
            "holding_duration": 0,           # 持仓时间（仓位在95%以上的时间）
            "closing_duration": 0            # 清仓耗时（从开始减仓到5%）
        }

        # 如果有现有数据，继承时间记录
        if existing_data:
            result["open_start_time"] = existing_data.get("open_start_time")
            result["position_95_time"] = existing_data.get("position_95_time")
            result["position_below_95_time"] = existing_data.get("position_below_95_time")
            result["position_5_time"] = existing_data.get("position_5_time")
            result["position_peak_time"] = existing_data.get("position_peak_time")
            result["position_start_closing_time"] = existing_data.get("position_start_closing_time")
            result["peak_progress"] = existing_data.get("peak_progress", 0)

        # 检查进度变化并更新时间记录
        if progress >= 95 and not result["position_95_time"]:
            # 首次达到95%
            beijing_time_str = get_beijing_time_str()
            result["position_95_time"] = beijing_time_str
            logger.info(f"仓位达到95%: {beijing_time_str}")

        if progress < 95 and result["position_95_time"] and not result["position_below_95_time"]:
            # 从95%以上降到95%以下
            beijing_time_str = get_beijing_time_str()
            result["position_below_95_time"] = beijing_time_str
            logger.info(f"仓位降到95%以下: {beijing_time_str}")

        # 跟踪峰值仓位
        if progress > result["peak_progress"]:
            result["peak_progress"] = progress
            result["position_peak_time"] = get_beijing_time_str()
            logger.info(f"仓位达到新峰值 {progress:.1f}%: {result['position_peak_time']}")

        # 检查是否开始清仓（仓位开始减少）
        if result["peak_progress"] > 0 and progress < result["peak_progress"] and not result["position_start_closing_time"]:
            # 仓位开始减少，记录开始清仓时间
            beijing_time_str = get_beijing_time_str()
            result["position_start_closing_time"] = beijing_time_str
            logger.info(f"仓位开始减少，开始清仓: {beijing_time_str} (从 {result['peak_progress']:.1f}% 降到 {progress:.1f}%)")

        if progress <= 5 and result["position_start_closing_time"] and not result["position_5_time"]:
            # 降到5%以下
            beijing_time_str = get_beijing_time_str()
            result["position_5_time"] = beijing_time_str
            logger.info(f"仓位降到5%: {beijing_time_str}")

        # 计算开仓耗时（从开始开仓到95%）
        if result["open_start_time"]:
            try:
                start_time = datetime.strptime(result["open_start_time"], "%Y-%m-%d %H:%M:%S").replace(tzinfo=BEIJING_TZ)
                if result["position_95_time"]:
                    # 已经达到95%，使用固定的开仓耗时
                    pos_95_time = datetime.strptime(result["position_95_time"], "%Y-%m-%d %H:%M:%S").replace(tzinfo=BEIJING_TZ)
                    result["opening_duration"] = int((pos_95_time - start_time).total_seconds())
                elif progress < 95:
                    # 还在开仓中（仓位小于95%），计算当前开仓时间
                    result["opening_duration"] = int((current_time - start_time).total_seconds())
                else:
                    # 刚达到95%但还没记录时间，使用当前时间
                    result["opening_duration"] = int((current_time - start_time).total_seconds())
            except Exception as e:
                logger.error(f"计算开仓耗时失败: {e}")
                result["opening_duration"] = 0

        # 计算持仓时间（仓位在95%以上的时间）
        if result["position_95_time"]:
            try:
                pos_95_time = datetime.strptime(result["position_95_time"], "%Y-%m-%d %H:%M:%S").replace(tzinfo=BEIJING_TZ)
                if result["position_below_95_time"]:
                    # 已经降到95%以下，计算实际持仓时间
                    pos_below_95_time = datetime.strptime(result["position_below_95_time"], "%Y-%m-%d %H:%M:%S").replace(tzinfo=BEIJING_TZ)
                    result["holding_duration"] = int((pos_below_95_time - pos_95_time).total_seconds())
                elif progress >= 95:
                    # 还在95%以上，计算当前持仓时间
                    result["holding_duration"] = int((current_time - pos_95_time).total_seconds())
            except Exception as e:
                logger.error(f"计算持仓时间失败: {e}")
                result["holding_duration"] = 0

        # 计算清仓耗时（从开始减仓到5%）
        if result["position_start_closing_time"]:
            try:
                start_closing_time = datetime.strptime(result["position_start_closing_time"], "%Y-%m-%d %H:%M:%S").replace(tzinfo=BEIJING_TZ)
                if result["position_5_time"]:
                    # 已经降到5%，计算完整清仓时间
                    pos_5_time = datetime.strptime(result["position_5_time"], "%Y-%m-%d %H:%M:%S").replace(tzinfo=BEIJING_TZ)
                    result["closing_duration"] = int((pos_5_time - start_closing_time).total_seconds())
                elif progress > 5:
                    # 正在清仓中（仓位大于5%），计算当前清仓时间
                    result["closing_duration"] = int((current_time - start_closing_time).total_seconds())
            except Exception as e:
                logger.error(f"计算清仓耗时失败: {e}")
                result["closing_duration"] = 0

        return result

    except Exception as e:
        logger.error(f"计算持仓指标失败: {e}")
        return {
            "progress": 0,
            "pnl": 0,
            "is_profit": False,
            "open_time": "",
            "open_start_time": None,
            "position_95_time": None,
            "position_below_95_time": None,
            "position_5_time": None,
            "opening_duration": 0,
            "holding_duration": 0,
            "closing_duration": 0
        }

@app.route('/')
def index():
    """主页面"""
    return render_template('index.html')

@app.route('/api/position', methods=['POST'])
def receive_position():
    """接收持仓数据的API接口"""
    try:
        data = request.get_json()

        # 验证必要字段
        required_fields = ['exchange', 'symbol', 'action', 'position']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"缺少必要字段: {field}"}), 400

        exchange = data['exchange']
        symbol = data['symbol']
        action = data['action']
        position = data['position']

        # 验证position字段
        position_required = ['time', 'position_side']
        for field in position_required:
            if field not in position:
                return jsonify({"error": f"position中缺少必要字段: {field}"}), 400

        position_side = position['position_side'].upper()
        if position_side not in ['LONG', 'SHORT']:
            return jsonify({"error": "position_side必须是LONG或SHORT"}), 400

        # 记录到日志文件
        log_to_file(data)

        # 更新内存数据
        with data_lock:
            if exchange not in positions_data:
                positions_data[exchange] = {}
            if symbol not in positions_data[exchange]:
                positions_data[exchange][symbol] = {}

            # 获取现有数据
            existing_data = positions_data[exchange][symbol].get(position_side, {})

            # 处理开仓开始时间记录
            if action == "open_pos":
                # 开始开仓，重置所有时间记录，使用北京时间
                beijing_time_str = get_beijing_time_str()
                existing_data = {
                    "open_start_time": beijing_time_str,
                    "position_95_time": None,
                    "position_below_95_time": None,
                    "position_5_time": None,
                    "position_peak_time": None,
                    "position_start_closing_time": None,
                    "peak_progress": 0,
                    "opening_duration": 0,
                    "holding_duration": 0,
                    "closing_duration": 0
                }
                logger.info(f"开始开仓(重置): {exchange} {symbol} {position_side} at {beijing_time_str}")

            # 存储原始数据和计算指标
            position_with_metrics = position.copy()
            metrics = calculate_position_metrics(position, existing_data)
            position_with_metrics.update(metrics)

            positions_data[exchange][symbol][position_side] = position_with_metrics

        # 通过WebSocket广播更新
        socketio.emit('position_update', {
            'exchange': exchange,
            'symbol': symbol,
            'position_side': position_side,
            'data': position_with_metrics
        })

        logger.info(f"收到持仓更新: {exchange} {symbol} {position_side}")
        return jsonify({"status": "success", "message": "数据接收成功"})

    except Exception as e:
        logger.error(f"处理持仓数据失败: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/positions', methods=['GET'])
def get_positions():
    """获取当前所有持仓数据"""
    with data_lock:
        return jsonify(positions_data)

@app.route('/api/positions/clear', methods=['POST'])
def clear_positions():
    """清除所有持仓数据"""
    try:
        with data_lock:
            # 清空内存中的持仓数据和trade数据
            positions_data.clear()
            trade_data.clear()
            logger.info("所有持仓数据和trade数据已清除")

        # 通过WebSocket广播清除事件
        socketio.emit('positions_cleared', {
            'message': '所有持仓数据已清除',
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        })

        return jsonify({
            "status": "success",
            "message": "所有持仓数据已清除",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        })

    except Exception as e:
        logger.error(f"清除持仓数据失败: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/update_position_by_trade', methods=['POST'])
def update_position_by_trade():
    """通过trade更新仓位的API接口"""
    try:
        data = request.get_json()

        # 验证必要字段
        required_fields = ['symbol', 'order_id', 'position_side', 'price', 'acc_quantity']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"缺少必要字段: {field}"}), 400

        symbol = data['symbol']
        order_id = data['order_id']
        position_side = data['position_side'].upper()
        price = float(data['price'])
        acc_quantity = float(data['acc_quantity'])

        # 可选字段
        exchange = data.get('exchange', 'default')

        # 检查是否为OKX交易所（acc_quantity为单笔成交量）
        is_okx_exchange = "Okx" in exchange or "OKX" in exchange or "okx" in exchange

        # 验证position_side
        if position_side not in ['LONG', 'SHORT']:
            return jsonify({"error": "position_side必须是LONG或SHORT"}), 400

        # 记录到日志文件
        log_to_file(data)

        # 更新内存数据
        with data_lock:
            # 初始化trade_data结构
            if exchange not in trade_data:
                trade_data[exchange] = {}
            if symbol not in trade_data[exchange]:
                trade_data[exchange][symbol] = {}
            if position_side not in trade_data[exchange][symbol]:
                trade_data[exchange][symbol][position_side] = {}

            if is_okx_exchange:
                # 对于OKX交易所，acc_quantity是单笔成交量，直接使用
                actual_quantity_change = acc_quantity

                # 更新trade记录 - 记录单笔成交
                order_trade_info = trade_data[exchange][symbol][position_side].get(order_id, {
                    "total_quantity": 0.0,  # 该订单的总成交量
                    "last_update_time": "",
                    "total_trades": 0
                })

                new_total_quantity = order_trade_info["total_quantity"] + acc_quantity
                trade_data[exchange][symbol][position_side][order_id] = {
                    "total_quantity": new_total_quantity,
                    "last_update_time": get_beijing_time_str(),
                    "total_trades": order_trade_info["total_trades"] + 1
                }

                logger.info(f"OKX单笔成交: {exchange} {symbol} {position_side}, order_id: {order_id}, "
                           f"单笔数量: {acc_quantity}, 订单总成交: {new_total_quantity}")
            else:
                # 对于其他交易所，acc_quantity是累计成交量
                order_trade_info = trade_data[exchange][symbol][position_side].get(order_id, {
                    "last_acc_quantity": 0.0,
                    "last_update_time": "",
                    "total_trades": 0
                })

                # 计算本次实际变化的数量（可能是增仓或减仓）
                last_acc_quantity = order_trade_info["last_acc_quantity"]
                actual_quantity_change = acc_quantity - last_acc_quantity

                # 更新trade记录
                trade_data[exchange][symbol][position_side][order_id] = {
                    "last_acc_quantity": acc_quantity,
                    "last_update_time": get_beijing_time_str(),
                    "total_trades": order_trade_info["total_trades"] + 1
                }

            # 初始化positions_data结构
            if exchange not in positions_data:
                positions_data[exchange] = {}
            if symbol not in positions_data[exchange]:
                positions_data[exchange][symbol] = {}

            # 获取现有仓位数据
            existing_position = positions_data[exchange][symbol].get(position_side, {})

            # 如果是第一次为该仓位方向创建数据，需要初始化
            if not existing_position:
                # 这是一个新的仓位，需要先调用open_pos
                logger.warning(f"尝试通过trade更新未开仓的仓位: {exchange} {symbol} {position_side}")
                return jsonify({"error": "请先调用open_pos开仓，然后再通过trade更新仓位"}), 400

            # 更新当前数量
            current_quantity = float(existing_position.get("current_quantity", 0))
            new_current_quantity = current_quantity + actual_quantity_change

            # 创建更新后的仓位数据
            updated_position = existing_position.copy()
            updated_position.update({
                "current_quantity": str(new_current_quantity),
                "current_price": str(price),
                "time": get_beijing_time_str()
            })

            # 重新计算指标
            metrics = calculate_position_metrics(updated_position, existing_position)
            updated_position.update(metrics)

            # 保存更新后的数据
            positions_data[exchange][symbol][position_side] = updated_position

        # 通过WebSocket广播更新
        socketio.emit('position_update', {
            'exchange': exchange,
            'symbol': symbol,
            'position_side': position_side,
            'data': updated_position
        })

        # 判断是增仓还是减仓
        operation_type = "增仓" if actual_quantity_change > 0 else "减仓" if actual_quantity_change < 0 else "无变化"

        if is_okx_exchange:
            logger.info(f"OKX通过trade更新仓位: {exchange} {symbol} {position_side}, order_id: {order_id}, "
                       f"单笔成交: {acc_quantity}, 仓位变化: {actual_quantity_change} ({operation_type})")
        else:
            logger.info(f"通过trade更新仓位: {exchange} {symbol} {position_side}, order_id: {order_id}, "
                       f"累计数量: {acc_quantity}, 实际变化: {actual_quantity_change} ({operation_type})")

        return jsonify({
            "status": "success",
            "message": f"仓位{operation_type}成功",
            "order_id": order_id,
            "acc_quantity": acc_quantity,
            "actual_quantity_change": actual_quantity_change,
            "new_current_quantity": new_current_quantity,
            "operation_type": operation_type
        })

    except Exception as e:
        logger.error(f"通过trade更新仓位失败: {e}")
        return jsonify({"error": str(e)}), 500

@socketio.on('connect')
def handle_connect():
    """WebSocket连接处理"""
    logger.info("客户端已连接")
    # 发送当前所有持仓数据
    with data_lock:
        emit('initial_data', positions_data)

@socketio.on('disconnect')
def handle_disconnect():
    """WebSocket断开连接处理"""
    logger.info("客户端已断开连接")

def update_time_metrics():
    """实时更新时间指标的后台任务"""
    while True:
        try:
            time.sleep(1)  # 每秒更新一次

            with data_lock:
                updated = False
                for exchange in positions_data:
                    for symbol in positions_data[exchange]:
                        for position_side in positions_data[exchange][symbol]:
                            position = positions_data[exchange][symbol][position_side]

                            # 创建一个虚拟的position_data来重新计算时间指标
                            beijing_time_str = get_beijing_time_str()
                            virtual_position_data = {
                                "time": beijing_time_str,
                                "open_quantity": position.get("open_quantity", 0),
                                "current_quantity": position.get("current_quantity", 0),
                                "open_avg_price": position.get("open_avg_price", 0),
                                "current_price": position.get("current_price", position.get("open_avg_price", 0)),
                                "position_side": position.get("position_side", "LONG")
                            }

                            # 重新计算指标
                            new_metrics = calculate_position_metrics(virtual_position_data, position)

                            # 检查是否有时间相关的更新
                            old_opening = position.get("opening_duration", 0)
                            old_holding = position.get("holding_duration", 0)
                            old_closing = position.get("closing_duration", 0)

                            new_opening = new_metrics.get("opening_duration", 0)
                            new_holding = new_metrics.get("holding_duration", 0)
                            new_closing = new_metrics.get("closing_duration", 0)

                            # 如果时间有变化，更新数据
                            if (old_opening != new_opening or
                                old_holding != new_holding or
                                old_closing != new_closing):

                                # 更新时间相关字段
                                position.update({
                                    "opening_duration": new_opening,
                                    "holding_duration": new_holding,
                                    "closing_duration": new_closing
                                })

                                updated = True

                                # 广播更新
                                socketio.emit('position_update', {
                                    'exchange': exchange,
                                    'symbol': symbol,
                                    'position_side': position_side,
                                    'data': position
                                })

                if updated:
                    logger.debug("实时更新了时间指标")

        except Exception as e:
            logger.error(f"实时更新时间指标失败: {e}")

def start_background_tasks():
    """启动后台任务"""
    # 启动实时更新线程
    update_thread = threading.Thread(target=update_time_metrics, daemon=True)
    update_thread.start()
    logger.info("实时时间更新线程已启动")

if __name__ == '__main__':
    logger.info("启动多交易所持仓可视化服务器...")
    logger.info("访问地址: http://localhost:5000")

    # 启动后台任务
    start_background_tasks()

    socketio.run(app, host='0.0.0.0', port=5000, debug=False, allow_unsafe_werkzeug=True)
